{"name": "f0ck_beta", "version": "3.6.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "prod": "npm run build && npm run start"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@modelcontextprotocol/sdk": "^1.15.0", "@types/nodemailer": "^6.4.17", "@types/socket.io-client": "^1.4.36", "@types/styled-components": "^5.1.34", "@upstash/context7-mcp": "^1.0.14", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "file-type": "^21.0.0", "install": "^0.13.0", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.525.0", "mongodb": "^6.17.0", "mongoose": "^8.16.2", "next": "^15.3.5", "next-auth": "^4.24.11", "nodemailer": "^7.0.5", "npm": "^11.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "sharp": "^0.34.2", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "styled-components": "^6.1.19", "styled-jsx": "^5.1.7", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/forms": "^0.5.10", "@types/bcryptjs": "^3.0.0", "@types/mongoose": "^5.11.97", "@types/node": "^22.13.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-config-next": "15.3.5", "postcss": "^8", "tailwindcss": "^3.4.11", "typescript": "^5"}, "description": "> ⚠️ **Development Notice**: This project is currently under active development. A public live version is online, but many functions are still being improved.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ninjazan420/f0ck_beta.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ninjazan420/f0ck_beta/issues"}, "homepage": "https://github.com/ninjazan420/f0ck_beta#readme"}