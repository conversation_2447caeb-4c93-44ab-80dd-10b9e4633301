'use client';

import { useState } from 'react';
import { CommentFilter } from './CommentFilter';
import { CommentList } from './CommentList';
import { Footer } from '@/components/Footer';
import { StatusBanner } from '@/components/StatusBanner';

export function CommentsPage() {
  const [filters, setFilters] = useState({
    username: '',
    searchText: '',
    dateFrom: '',
    dateTo: '',
    minLikes: 0
  });
  
  const [infiniteScroll, setInfiniteScroll] = useState(false);
  const [showBanner, setShowBanner] = useState(false);
  
  const handleInfiniteScrollToggle = (enabled: boolean) => {
    setInfiniteScroll(enabled);
    setShowBanner(true);
  };

  return (
    <div className="min-h-screen flex flex-col">
      
      {/* Status Banner */}
      <StatusBanner
        show={showBanner}
        message={infiniteScroll ? 'Infinite scroll activated' : 'Infinite scroll deactivated'}
        type="success"
      />
      
      <div className="container mx-auto px-4 py-4 max-w-4xl flex-grow">
        <div className="space-y-6">
          <CommentFilter 
            filters={filters} 
            onFilterChange={setFilters}
            infiniteScroll={infiniteScroll}
            onToggleInfiniteScroll={handleInfiniteScrollToggle}
          />
          
          <CommentList 
            filters={filters}
            infiniteScroll={infiniteScroll}
          />
        </div>
      </div>
      
      <Footer />
    </div>
  );
}
